use dioxus::{
    logger::tracing::debug,
    prelude::*,
};
use dioxus_i18n::prelude::*;
pub use dioxus_i18n::tid as t;
use unic_langid::{
    LanguageIdentifier,
    langid,
};

use crate::state::AppState;

const ENGLISH: LanguageIdentifier = langid!("en-US");
const SPANISH: LanguageIdentifier = langid!("es-ES");
const FRENCH: LanguageIdentifier = langid!("fr-FR");
const GERMAN: LanguageIdentifier = langid!("de-DE");
const ITALIAN: LanguageIdentifier = langid!("it-IT");
const RUSSIAN: LanguageIdentifier = langid!("ru-RU");

#[component]
pub fn I18nProvider(children: Element) -> Element {
    let lang_id: LanguageIdentifier = match use_context::<AppState>().language.as_str() {
        "es-ES" => SPANISH,
        "fr-FR" => FRENCH,
        "de-DE" => GERMAN,
        "it-IT" => ITALIAN,
        "ru-RU" => RUSSIAN,
        _ => ENGLISH,
    };
    debug!("Locale: {lang_id}");
    use_init_i18n(|| {
        I18nConfig::new(ENGLISH).with_locale((ENGLISH, include_str!("locales/en-US/messages.ftl")))
    });
    i18n().set_language(lang_id);

    return rsx!({ children });
}
