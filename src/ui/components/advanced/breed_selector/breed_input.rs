use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, PartialEq, <PERSON><PERSON>)]
pub struct BreedInputProps {
    pub value:          String,
    pub on_change:      EventHandler<Event<FormData>>,
    pub on_focus:       EventHandler<Event<FocusData>>,
    pub on_click:       EventHandler<Event<MouseData>>,
    pub on_clear:       EventHandler<Event<MouseData>>,
    #[props(default = false)]
    pub disabled:       bool,
    #[props(default = false)]
    pub is_recognizing: bool,
}

#[component]
pub fn BreedInput(props: BreedInputProps) -> Element {
    let handle_clear_click = move |evt: Event<MouseData>| {
        evt.stop_propagation();
        props.on_clear.call(evt);
    };

    rsx!(
      div { class: "relative w-full",
        input {
          value: "{props.value}",
          oninput: move |evt| props.on_change.call(evt),
          onclick: move |evt| props.on_click.call(evt),
          onfocus: move |evt| props.on_focus.call(evt),
          placeholder: "Search for a breed...",
          disabled: props.disabled,
          class: "w-full pr-16 border rounded px-3 py-2", // Added basic styling
        }
        div { class: "absolute inset-y-0 right-0 flex items-center pr-3",
          if !props.value.is_empty() {
            button {
              class: "h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer mr-2",
              onclick: handle_clear_click,
              "❌"
            }
          }
          if props.is_recognizing {
            div { class: "h-4 w-4 animate-pulse text-pet-purple pointer-events-none",
              "✨"
            }
          } else {
            div { class: "h-4 w-4 text-gray-400 pointer-events-none", "🔍" }
          }
        }
      }
    )
}
