// mod breed_autocomplete;
// pub use breed_autocomplete::BreedAutocomplete;

// mod breed_dropdown;
// pub use breed_dropdown::BreedDropdown;

// mod breed_input;
// pub use breed_input::BreedInput;

// mod breed_item;
// pub use breed_item::BreedItem;


use dioxus::prelude::*;

// use crate::{
//     i18n::*,
//     models::Breed,                                 // Assuming Breed struct is in models
//     ui::components::breed_autocomplete::BreedItem, // Placeholder for now
//     ui::components::breed_autocomplete::BreedAutocomplete, // Placeholder for now
// };

// #[derive(Props, PartialEq, Clone)]
// pub struct BreedDropdownProps<'a> {
//     pub breeds:                   Vec<Breed>,
//     pub selected_breed:           String,
//     pub on_breed_select:          EventHandler<String>,
//     pub get_localized_breed_name: &'a dyn Fn(&str) -> String,
// }

// #[component]
// pub fn BreedDropdown<'a>(cx: Scope<'a>, props: BreedDropdownProps<'a>) -> Element {
//     let i18n = I18n::new();

//     rsx!(
//       div { class: "absolute z-50 mt-1 bg-white shadow-lg rounded-md border border-gray-200
// overflow-hidden w-full max-h-60 overflow-y-auto",         if props.breeds.is_empty() {
//           div { class: "p-3 text-center text-gray-500", {t!("common-no-r")}esults")} }
//         } else {
//           div {
//             for breed in props.breeds.iter() {
//               BreedItem {
//                 key: "{breed.name}",
//                 breed: breed.clone(),
//                 is_selected: breed.name == props.selected_breed,
//                 on_click: move |_| props.on_breed_select.call(breed.name.clone()),
//                 display_name: (props.get_localized_breed_name)(&breed.name),
//               }
//             }
//           }
//         }
//       }
//     )
// }


// #[derive(Props, PartialEq, Clone)]
// pub struct BreedSelectorProps {
//     pub selected_breed:    Option<String>,
//     pub on_breed_selected: EventHandler<String>,
//     #[props(optional)]
//     pub photo_url:         Option<String>,
//     #[props(default = true)]
//     pub show_label:        bool,
// }

// #[component]
// pub fn BreedSelector(props: BreedSelectorProps) -> Element {
//     rsx! {
//       div {
//         if props.show_label {
//           label { class: "block text-sm font-medium mb-2", {t!("onboarding-breed-o")}ptional")} }
//         }
//         BreedAutocomplete {
//           value: props.selected_breed.clone(),
//           on_change: move |b: String| props.on_breed_selected.call(b),
//           image_url: props.photo_url.clone(),
//         }
//         if props.show_label {
//           p { class: "text-xs text-gray-500 mt-1", {t!("onboarding-breed-d")}escription")} }
//         }
//       }
//     }
// }
