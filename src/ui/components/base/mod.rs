pub mod alert;
pub use alert::*;

pub mod avatar;
pub use avatar::*;

pub mod badge;
pub use badge::*;

pub mod button;
pub use button::*;

pub mod dropdown;
pub use dropdown::*;

pub mod icon;
pub use icon::*;

pub mod input;
pub use input::*;

pub mod label;
pub use label::*;

pub mod progress_bar;
pub use progress_bar::*;

pub mod select;
pub use select::*;

pub mod switch;
pub use switch::*;


#[derive(PartialEq, Eq, Clone, Copy)]
pub enum Size {
    Xs,
    Sm,
    Md,
    Lg,
    Xl,
    // Xl2,
    // Xl3,
}

impl Default for Size {
    fn default() -> Self { Self::Md }
}
