use dioxus::prelude::*;

use crate::ui::layout::Row;


#[derive(<PERSON>ps, PartialEq, Eq, Clone)]
pub struct ProgressBarProps {
    #[props(default)]
    pub title:         String,
    pub current_value: u32,
    pub total_value:   u32,
    pub unit:          String,
}

#[component]
pub fn ProgressBar(props: ProgressBarProps) -> Element {
    let progress_percentage = (props.current_value as f32 / props.total_value as f32 * 100.0).min(100.0);

    rsx!(
      div { class: "w-full",
        Row { class: "mb-2 text-sm font-medium ",
          span { class: "text-gray-600", "{props.title}" }
          span { class: "font-semibold",
            "{props.current_value}/{props.total_value} {props.unit}"
          }
        }
        div { class: "h-2 bg-background-dark/30 rounded-full",
          div {
            class: "h-2 bg-secondary rounded-full",
            style: "width: {progress_percentage}%",
          }
        }
      }
    )
}
