use std::ops::Deref;

use dioxus::prelude::*;
use dioxus_free_icons::IconShape;

use crate::ui::components::base::Size;


#[derive(<PERSON><PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct IconRef {
    icon_shape: Option<&'static dyn IconShape>,
}
impl PartialEq for IconRef {
    fn eq(&self, other: &Self) -> bool {
        match (self.icon_shape, other.icon_shape) {
            (Some(a), Some(b)) => std::ptr::eq(a, b),
            (None, None) => true,
            _ => false,
        }
    }
}

impl IconRef {
    pub fn new(icon: &'static dyn IconShape) -> Self {
        Self {
            icon_shape: Some(icon),
        }
    }

    pub fn none() -> Self {
        Self {
            icon_shape: None
        }
    }

    pub fn is_some(&self) -> bool { self.icon_shape.is_some() }

    pub fn is_none(&self) -> bool { self.icon_shape.is_none() }

    pub fn call(icon: &'static dyn IconShape) -> Self {
        Self {
            icon_shape: Some(icon),
        }
    }
}

impl From<&'static dyn IconShape> for IconRef {
    fn from(icon: &'static dyn IconShape) -> Self {
        Self {
            icon_shape: Some(icon),
        }
    }
}

impl From<Option<&'static dyn IconShape>> for IconRef {
    fn from(icon: Option<&'static dyn IconShape>) -> Self {
        Self {
            icon_shape: icon
        }
    }
}

impl AsRef<dyn IconShape> for IconRef {
    fn as_ref(&self) -> &'static dyn IconShape { self.icon_shape.expect("IconRef is None") }
}

impl Deref for IconRef {
    // type Target = dyn IconShape;
    type Target = dyn IconShape;

    fn deref(&self) -> &Self::Target { self.icon_shape.expect("IconRef is None") }
}


/// Icon component Props
#[derive(PartialEq, Props, Clone)]
pub struct IconProps {
    /// The icon shape to use.
    // pub icon:  &'static dyn IconShape,
    pub icon: IconRef,
    /// The width and height of the `<svg>` element. Defaults to 20.
    #[props(default)]
    pub size:  Size,
    /// The color to use for filling the icon. Defaults to "currentColor".
    #[props(default = "currentColor".to_string())]
    pub fill:  String,
    /// An class for the `<svg>` element.
    #[props(default = String::new())]
    pub class: String,
    /// An accessible, short-text description for the icon.
    pub title: Option<String>,
    /// The style of the `<svg>` element.
    pub style: Option<String>,
}

/// Icon component which generates SVG elements
#[allow(non_snake_case)]
pub fn Icon(props: IconProps) -> Element {
    let size = match props.size {
        Size::Xs => 14,
        Size::Sm => 18,
        Size::Md => 24,
        Size::Lg => 32,
        Size::Xl => 48,
    };

    if props.icon.is_none() {
        return rsx! {
          div { style: "width: {size}px; height: {size}px;" }
        };
    }

    let icon = props.icon.as_ref();
    let (fill, stroke, stroke_width) = icon.fill_and_stroke(&props.fill);

    rsx!(
      div {
        svg {
          class: "transition-transform duration-200 {props.class}",
          style: props.style,
          height: size,
          width: size,
          view_box: "{icon.view_box()}",
          xmlns: "{icon.xmlns()}",
          fill,
          stroke,
          stroke_width,
          stroke_linecap: "{icon.stroke_linecap()}",
          stroke_linejoin: "{icon.stroke_linejoin()}",
          if let Some(title_text) = props.title {
            title { "{title_text}" }
          }
          {icon.child_elements()}
        }
      }
    )
}
