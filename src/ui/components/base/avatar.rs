use dioxus::prelude::*;

use crate::ui::components::base::Size;


#[derive(Props, PartialEq, Clone)]
pub struct AvatarProps {
    #[props(optional)]
    pub src:     Option<String>,
    pub name:    String,
    #[props(default)]
    pub size:    Size,
    #[props(optional)]
    pub class:   Option<String>,
    #[props(optional)]
    pub onclick: Option<EventHandler<MouseEvent>>,
}

#[component]
pub fn Avatar(props: AvatarProps) -> Element {
    let size_classes = match props.size {
        Size::Xs => "h-10 w-10",
        Size::Sm => "h-12 w-12",
        Size::Md => "h-16 w-16",
        Size::Lg => "h-24 w-24",
        Size::Xl => "h-32 w-32",
    };

    let initials: String = props
        .name
        .split_whitespace()
        .filter_map(|s| s.chars().next())
        .collect::<String>()
        .to_uppercase();

    rsx!(
      div {
        class: "{size_classes} rounded-full shadow-md hover:shadow-lg transition-shadow duration-200
    {props.class.as_deref().unwrap_or(\"\")}",
        onclick: move |evt| {
            if let Some(handler) = &props.onclick {
                handler.call(evt);
            }
        },
        if let Some(image_src) = &props.src {
          img {
            src: "{image_src}",
            alt: "{props.name}",
            class: "w-full h-full object-cover rounded-full",
          }
        } else {
          div { class: "w-full h-full flex items-center justify-center rounded-full pet-gradient
    text-white",
            "{initials}"
          }
        }
      }
    )
}
