/* Dropdown Menu Styles */
.dropdown-menu {
  position: relative;
  display: inline-block;
}

.dropdown-menu-trigger {
  padding: 0;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  width: 100%;
}

.dropdown-menu-content {
  /* position: absolute; */
  /* top: 100%; */
  position: sticky;
  width: 100vw;
  /* overflow: clip; */
  /* top: 0; */

  /* z-index: 1000; */
  /* left: 0; */
  animation: slideIn 0.1s ease-out;

  /* Animation properties */
  opacity: 1;
  transition: transform 0.1s ease, opacity 0.1s ease;
  /* transform: translateY(-8px) scale(0.95); */
  transform: translateY(-100px);

  --tw-backdrop-blur: blur(var(--blur-3xl));
  -webkit-backdrop-filter: var(--tw-backdrop-blur);
  backdrop-filter: var(--tw-backdrop-blur);
  /* min-width: 200px; */
  /* padding: 0.25rem; */
  /* border-radius: 0.5rem; */
  /* margin-top: 4px; */
  /* background-color: var(--dark, var(--primary-color-5)) var(--light, var(--primary-color)); */
  /* box-shadow: inset 0 0 0 1px var(--dark, var(--primary-color-7)) var(--light, var(--primary-color-6)); */
}

.dropdown-menu-content[data-state="closed"] {
  pointer-events: none;
  animation: dropdown-menu-content-animate-out 100ms ease-in forwards;
}

@keyframes dropdown-menu-content-animate-out {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }

  100% {
    opacity: 0;
    /* transform: scale(0.95) translateY(-2px); */
    transform: translateY(-100px);
  }
}

.dropdown-menu-content[data-state="open"] {
  animation: dropdown-menu-content-animate-in 100ms ease-out forwards;
}

@keyframes dropdown-menu-content-animate-in {
  0% {
    opacity: 0;
    /* transform: scale(0.95) translateY(-2px); */
    transform: translateY(-100px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dropdown-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  /* justify-content: space-between; */
  cursor: pointer;
  outline: none;
  user-select: none;
  /* font-size: 14px; */
  /* padding: 8px 12px; */
  /* border-radius: calc(0.5rem - 0.25rem); */
  /* color: var(--secondary-color-4); */
}

.dropdown-menu-item[data-disabled="true"] {
  /* color: var(--secondary-color-5); */
  color: var(--secondary-color-5);
  cursor: not-allowed;
}

.dropdown-menu-item:hover:not([data-disabled="true"]),
.dropdown-menu-item:focus-visible {
  /* background: var(--dark, var(--primary-color-7)) var(--light, var(--primary-color-4)); */
  color: var(--secondary-color-1);
}
