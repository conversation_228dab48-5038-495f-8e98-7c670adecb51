use dioxus::{
    logger::tracing::{
        debug,
        info,
    },
    prelude::*,
};

use crate::{
    i18n::t,
    state::{use_state_by_event, AppEvent, RadioReducer, AppAction},
    ui::layout::{
        <PERSON>,
        PageHeader,
        Section,
    },
};

mod account_section;
use account_section::AccountSection;

mod app_settings_section;
use app_settings_section::AppSettingsSection;

mod notifications_section;
use notifications_section::NotificationsSection;


#[component]
pub fn Settings() -> Element {
    info!("Settings()");
    let mut state = use_state_by_event(AppEvent::UserUpdated);
    let state_snapshot = state.read();

    let is_authenticated = use_signal(|| false);
    let auth_dialog_open = use_signal(|| false);
    let user = move || state_snapshot.user.clone();

    use_effect(move || {
        debug!("Notifications enabled: {}", state.read().user.notifications_enabled);
    });

    let handle_notification_toggle = move |enabled: bool| {
        debug!("Toggling notifications to: {}", enabled);
        state.apply(AppAction::NotificationsToggle {
            enabled,
        });
    };

    let handle_unit_system_change = move |system: String| {
        // user_profile.set(UserProfile {
        //     unit_system: system.clone(),
        //     ..user_profile.clone()
        // });
    };

    let handle_auth_success = move || {
        // auth_dialog_open.set(false);
        // is_authenticated.set(true);
        // user.set(Some(UserProfile {
        //     unit_system:           "metric".to_string(), // Default for logged in user
        //     notifications_enabled: true,
        // }));
        // In a real app, fetch actual user profile
    };

    rsx!(
        Page {
            PageHeader { title: t!("settings-settings"), show_back_button: false }

            NotificationsSection {}
            AppSettingsSection {
                unit_system: user().unit_system,
                on_unit_system_change: handle_unit_system_change,
            }
            AccountSection {}

            // AuthDialog {
            //   open: *auth_dialog_open.get(),
            //   on_open_change: move |val| auth_dialog_open.set(val),
            //   on_success: handle_auth_success,
            // }
            "."
        }
    )
}
