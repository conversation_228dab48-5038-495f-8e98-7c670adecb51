use dioxus::{
    logger::tracing::debug,
    prelude::*,
};
use dioxus_free_icons::icons::ld_icons::LdCamera;

use crate::ui::components::base::{
    Icon,
    IconRef,
    Size,
};
use crate::{
    i18n::t,
    models::{
        Dog,
        Food,
    },
    state::{
        AppAction,
        AppEvent,
        RadioReducer,
        use_state_by_event,
    },
    // ui::components::{
    //     BreedSelector, // Placeholder, will need to be implemented
    //     DeleteDogDialog,
    //     EditDogProfileModal,
    //     FoodManagement,
    // },
    ui::components::base::{
        Button,
        ButtonVariant,
    },
    ui::layout::{
        Card,
        Page,
        PageHeaderProfile,
        Row,
        Section,
        Title,
    },
};

mod editable_field;
use editable_field::EditableField;

#[component]
pub fn DogProfile() -> Element {
    debug!("DogProfile()");
    let mut state = use_state_by_event(AppEvent::DogUpdated);
    let state_snapshot = state.read();
    let dogs = &state_snapshot.dogs;
    if dogs.is_empty() {
        // let navigator = use_navigator();
        return rsx! { "No dog selected" };
    }
    let dog = state_snapshot.get_selected_dog().unwrap();
    let mut dog_mut = move || {
        // let mut state_snapshot_mut = state.write();
        // let dog_mut = state_snapshot_mut.get_selected_dog_mut();
        // let dog_mut = state.write().get_selected_dog_mut();
        // debug!("DogProfile(): dog_mut: {:?}", dog_mut);
        // dog_mut.unwrap()
        &mut state.write().get_selected_dog_mut().unwrap()
    };


    let handle_name_change = move |new_name: String| {
        dog_mut().name = new_name;
    };

    let handle_birthday_change = move |new_birthday: String| {
        debug!("on_change(): {}", new_birthday);
        state.write().get_selected_dog_mut().unwrap().birthday = Some(new_birthday.parse().unwrap());
    };
    let handle_breed_change = move |new_breed: String| {
        debug!("on_change(): {}", new_breed);
        state.write().get_selected_dog_mut().unwrap().breed = new_breed;
    };
    let handle_weight_change = move |new_weight: String| {
        debug!("on_change(): {}", new_weight);
        state.write().get_selected_dog_mut().unwrap().weight = Some(new_weight.parse().unwrap());
    };
    let handle_size_change = move |new_size: String| {
        debug!("on_change(): {}", new_size);
        state.write().get_selected_dog_mut().unwrap().size = Some(new_size);
    };

    rsx! {
      Page {
        PageHeaderProfile { dog: dog.clone(), dogs: dogs.clone() }

        Section { colored: true,
          // * Photo
          div {
            class: "rounded-3xl overflow-clip col items-center text-gray-300 hover:text-gray-200 mb-14",
            class: if dog.image.is_some() { "relative shadow-xl" } else { "shadow-glow-sm border-2 border-dashed border-secondary p-6 bg-secondary/5 text-secondary/80 hover:text-secondary m-6 mb-4" },
            style: if dog.image.is_some() { "aspect-ratio: 4/3" },
            if let Some(image_url) = &dog.image {
              img {
                src: "{image_url}",
                alt: "{dog.name}",
                class: "absolute w-full h-full object-cover",
              }
            }
            div { class: "mt-2 flex text-sm",
              label {
                r#for: "file-upload",
                class: "relative cursor-pointer rounded-md font-medium",
                Icon {
                  size: Size::Xl,
                  icon: IconRef::new(&LdCamera),
                }
                if dog.image.is_none() {
                  span { class: "text-gray-800", {t!("dog-profile-upload-photo")} }
                }
                input {
                  id: "file-upload",
                  name: "file-upload",
                  r#type: "file",
                  class: "sr-only",
                  accept: "image/*",
                                // onChange: handlePhotoUpload,
                }
              }
            }
          }

          div { class: "space-y-5 my-4 text-sm",
            EditableField {
              label: t!("dog-profile-name"),
              value: dog.name.clone(),
              // on_change: move |new_name: String| handle_name_change(*dog.clone(), new_name),
              on_change: handle_name_change,
            }
            EditableField {
              label: t!("dog-profile-birthday"),
              value: dog.birthday.map_or_else(|| "Not set".to_string(), |d| d.to_string()),
              on_change: handle_birthday_change,
            }
            EditableField {
              label: t!("dog-profile-breed"),
              value: dog.breed.clone(),
              on_change: handle_breed_change,
            }
            EditableField {
              label: t!("dog-profile-weight"),
              value: dog.weight.unwrap_or_default().to_string(),
              on_change: handle_weight_change,
            }
            EditableField {
              label: t!("dog-profile-size"),
              value: dog.size.as_deref().unwrap_or("Not set"),
              on_change: handle_size_change,
            }
          }

          Button {
            variant: ButtonVariant::Destructive,
            class: "w-full bg-dog-red hover:bg-dog-red/90",
            // onclick: move |_| show_delete_dialog.set(true),
            {t!("common-delete")}
            {t!("common-dog-profile")}
          }
        }

        // DeleteDogDialog {
        //   is_open: *show_delete_dialog(),
        //   on_close: move || show_delete_dialog.set(false),
        //   on_delete: handle_delete,
        //   dog_name: dog.name.clone(),
        // }
        ""

        div { class: "px-5 py-4 border-t",
          // FoodManagement {
          //   pet_foods: dog.preferred_foods.clone(),
          //   food_history: dog.food_history.clone(),
          //   on_food_added: handle_add_pet_food,
          //   on_food_removed: handle_remove_pet_food,
          //   on_food_preferred: handle_food_preferred,
          // }
          ""
        }
      }
    }
}

// let mut show_delete_dialog = use_signal(|| false);
// let mut is_editing_breed = use_signal(|| false);

// let mut pet_breed_state = use_signal(|| dog.breed.clone());

// let handle_edit_breed = move |_: &MouseEvent| {
//     is_editing_breed.set(true);
// };

// let handle_save_breed = move |_: &MouseEvent| {
//     // if let Some(mut state) = state.write() {
//     //     if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//     //         p.breed = pet_breed_state().clone();
//     //     }
//     // }
//     is_editing_breed.set(false);
// };

// let handle_delete = move |_: &MouseEvent| {
//     // if let Some(mut state) = state.write() {
//     //     state.pets.retain(|p| p.id != pet_id);
//     //     if state.pets.is_empty() {
//     //         navigator.push("/");
//     //     }
//     // }
//     show_delete_dialog.set(false);
// };

// let handle_food_preferred = move |food_id: String, is_preferred: bool, _start_date: Option<String>| {
//     // if let Some(mut state) = state.write() {
//     //     if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//     //         if let Some(food) = p.preferred_foods.iter_mut().find(|f| f.id == food_id) {
//     //             food.is_preferred = is_preferred;
//     //         }
//     //     }
//     // }
// };

// let handle_add_pet_food = move |food: Food| {
//     // if let Some(mut state) = state.write() {
//     //     if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//     //         p.preferred_foods.push(food);
//     //     }
//     // }
// };

// let handle_remove_pet_food = move |food_id: String| {
//     // if let Some(mut state) = state.write() {
//     //     if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//     //         p.preferred_foods.retain(|f| f.id != food_id);
//     //     }
//     // }
// };
