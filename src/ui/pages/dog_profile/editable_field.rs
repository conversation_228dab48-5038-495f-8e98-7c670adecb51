use dioxus::{
    logger::tracing::debug,
    prelude::*,
};
use dioxus_free_icons::icons::ld_icons::LdPencil;

use crate::{
    i18n::t,
    ui::{
        components::base::{
            Icon,
            IconRef,
            <PERSON>ze,
        },
        layout::Row,
    },
};

#[derive(<PERSON><PERSON>, PartialEq, Clone)]
pub struct EditableFieldProps {
    pub label:          String,
    pub value:          String,
    pub on_change:      EventHandler<String>,
    #[props(optional)]
    pub on_focus:       EventHandler<FocusEvent>,
    // pub on_click:       EventHandler<MouseEvent>,
    // #[props(optional)]
    // pub on_clear:       EventHandler<MouseEvent>,
    #[props(default)]
    pub placeholder:    String,
    #[props(default = false)]
    pub disabled:       bool,
    #[props(default = false)]
    pub is_recognizing: bool,
}

#[component]
pub fn EditableField(props: EditableFieldProps) -> Element {
    let mut is_editing = use_signal(|| false);
    let mut input_value = use_signal(|| props.value.clone());

    let handle_edit = move |_| {
        is_editing.set(true);
    };

    let mut handle_save = move |_| {
        props.on_change.call(input_value());
        is_editing.set(false);
    };

    rsx! {
      Row {
        span {
          {props.label}
          ":"
        }
        if is_editing() {
          div { class: "flex items-center",
            input {
              value: "{props.value}",
              oninput: move |evt| {
                  input_value.set(evt.value());
              },
              // oninput: move |evt| props.on_change.call(evt.value()),
              // onclick: move |evt| props.on_click.call(evt),
              // onfocus: move |evt| props.on_focus.call(evt),
              // onchange: move |evt| props.on_change.call(evt.value()),
              class: "border rounded px-2 py-1 mr-2",
            }
            button {
              class: "text-secondary rounded-full",
              onclick: handle_save,
              {t!("common-save")}
            }
          }
        } else {
          div { class: "flex items-center",
            span { class: "text-sm font-medium capitalize mr-4", "{props.value}" }
            button {
              class: "text-secondary rounded-full",
              onclick: handle_edit,
              Icon { size: Size::Xs, icon: IconRef::new(&LdPencil) }
            }
          }
        }
        ""
      }
    }
}
