use dioxus::{
    logger::tracing::debug,
    prelude::*,
};
use dioxus_free_icons::icons::ld_icons::LdChevronDown;

use crate::{
    i18n::t,
    models::Dog,
    state::{
        AppAction,
        AppEvent,
        RadioReducer,
        use_state_by_event,
    },
    ui::{
        components::base::{
            Avatar,
            Button,
            ButtonVariant,
            DropdownMenu,
            DropdownMenuItem,
            Icon,
            IconRef,
            Size,
        },
        layout::{
            Col,
            Row,
        },
    },
};


const HEADER_PT: &str = if cfg!(any(target_os = "ios", target_os = "android")) {
    "pt-6"
} else {
    "pt-0"
};
const HEADER_MT: &str = if cfg!(any(target_os = "ios")) {
    "-mt-40"
} else {
    "-mt-1"
};


#[component]
pub fn PageHeaderProfile(dog: Dog, dogs: Vec<Dog>) -> Element {
    let handle_pet_select = move |id: String| {
        use_state_by_event(AppEvent::DogUpdated).apply(AppAction::DogSelect {
            id,
        });
    };

    // debug!("PageHeaderProfile: pt = {}, mt = {}", HEADER_PT, HEADER_MT);

    rsx! {
        div { class: "page-header {HEADER_MT} {HEADER_PT}",
            // class: "page-header",
            // style: "padding-top: env(safe-area-inset-top, 0);",
            DropdownMenu {
                class: "w-full",
                trigger: rsx! {
                    Row { class: "mt-2 items-start",
                        Col {
                            p { class: "text-black/80 text-sm uppercase",
                                // {dog.breed.as_deref().unwrap_or("Unknown breed")}
                                {dog.breed.clone()}
                                " • "
                                {dog.get_age()}
                            }
                            Row { class: "gap-0 mt-1 justify-start",
                                h1 { {dog.name.clone()} }
                                Icon {
                                    class: "text-black/80 ml-1",
                                    size: Size::Xs,
                                    icon: IconRef::new(&LdChevronDown),
                                }
                            }
                        }
                        Avatar {
                            class: "mt-0.5",
                            name: dog.name.clone(),
                            src: dog.image.clone(),
                            size: Size::Sm,
                            onclick: move |_| {
                                use_navigator().push(format!("/dog/{}", dog.id));
                            },
                        }
                    }
                },
                content_class: "page-content backdrop-blur-3xl border-b-2 border-b-background-dark rounded-b-4xl shadow-xl -ml-5 pt-2 pb-3",
                for (idx , dog) in dogs.iter().cloned().enumerate() {
                    DropdownMenuItem {
                        value: "{dog.id.clone()}",
                        index: idx,
                        disabled: true,
                        class: "flex gap-2 justify-start",
                        on_select: move |_| {
                            handle_pet_select(dog.id.clone());
                        },
                        Avatar {
                            name: dog.name.clone(),
                            src: dog.image.clone(),
                            size: Size::Xs,
                        }
                        span { {dog.name} }
                    }
                }
                hr { class: "my-2 h-0.5 bg-secondary/10" }
                Button {
                    variant: ButtonVariant::Ghost,
                    class: "mt-2 pt-1 pl-0",
                    onclick: move |_| {
                        use_navigator().push("/add-dog");
                    },
                    "+ "
                    {t!("common-add-another-dog")}
                }
            }
        }
    }
}

#[derive(PartialEq, Props, Clone)]
pub struct PageHeaderProps {
    pub title:            String,
    #[props(default = true)]
    pub show_back_button: bool,
    #[props(default = "/".to_string())]
    pub back_path:        String,
    #[props(optional)]
    pub gradient_class:   Option<String>,
    #[props(optional)]
    pub children:         Element,
}

#[component]
pub fn PageHeader(props: PageHeaderProps) -> Element {
    // let state = use_context::<AppState>();
    let navigator = use_navigator();

    // let gradient_class = use_memo(move || {
    //     if props.gradient_class.is_some() {
    //         return props.gradient_class.clone().unwrap();
    //     }
    //     match state.theme {
    //         Theme::Light => "bg-linear-to-b from-pet-purple to-pet-darkPurple".to_string(),
    //         Theme::Dark => "bg-linear-to-b from-pet-darkPurple to-pet-navy".to_string(),
    //         Theme::Colored => "bg-transparent".to_string(),
    //     }
    // });

    rsx! {
        div { class: "page-header {HEADER_MT} {HEADER_PT}",
            Col { class: "w-full mt-3",
                Row {
                    if props.show_back_button {
                        button {
                            class: "rounded-full p-2 bg-black hover:bg-black/90 transition-colors",
                            onclick: move |_| {
                                navigator.push(props.back_path.as_str());
                            },
                            // Placeholder for ArrowLeft icon
                            div { class: "h-5 w-5", "⬅️" }
                        }
                    }
                    h1 {
                        // class: {if props.show_back_button "ml-3" else ""},
                        {props.title}
                    }
                }
                {props.children}
            }
        }
    }
}
